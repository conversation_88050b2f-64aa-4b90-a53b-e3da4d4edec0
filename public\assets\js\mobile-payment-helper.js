/**
 * Mobile Payment Helper for APK/WebView environments
 * Handles UPI app launching and payment gateway optimization
 */

// Global mobile payment utilities
window.MobilePaymentHelper = {
    
    /**
     * Check if running in mobile app/APK environment
     */
    isMobileApp: function() {
        const userAgent = navigator.userAgent;

        // Check for APK/WebView indicators
        const isWebView = userAgent.includes('wv') ||
                         userAgent.includes('WebView') ||
                         userAgent.includes('Android WebView');

        const isMobileBrowser = (userAgent.includes('Version/') && userAgent.includes('Mobile Safari')) ||
                               window.navigator.standalone === false;

        const isAndroidMobile = userAgent.includes('Android') && userAgent.includes('Mobile');

        // Additional check for APK environment
        const isAPKEnvironment = window.location.protocol === 'https:' &&
                                userAgent.includes('Android') &&
                                (isWebView || userAgent.includes('Chrome/'));

        return isWebView || isMobileBrowser || isAndroidMobile || isAPKEnvironment;
    },

    /**
     * Check if running in WebView specifically
     */
    isWebView: function() {
        const userAgent = navigator.userAgent;
        return userAgent.includes('wv') ||
               userAgent.includes('WebView') ||
               userAgent.includes('Android WebView') ||
               (userAgent.includes('Version/') && userAgent.includes('Mobile Safari'));
    },

    /**
     * Launch UPI apps with multiple fallback methods
     */
    launchUPIApp: function(merchantVPA, merchantName, amount, transactionNote, callback) {
        const upiIntents = [
            // Standard UPI intent
            `upi://pay?pa=${merchantVPA}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=INR&tn=${encodeURIComponent(transactionNote)}`,
            // Android intent format for WebView - PhonePe
            `intent://pay?pa=${merchantVPA}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=INR&tn=${encodeURIComponent(transactionNote)}#Intent;scheme=upi;package=com.phonepe.app;end`,
            // Google Pay intent
            `intent://pay?pa=${merchantVPA}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=INR&tn=${encodeURIComponent(transactionNote)}#Intent;scheme=upi;package=com.google.android.apps.nbu.paisa.user;end`,
            // Paytm intent
            `intent://pay?pa=${merchantVPA}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=INR&tn=${encodeURIComponent(transactionNote)}#Intent;scheme=upi;package=net.one97.paytm;end`,
            // BHIM intent
            `intent://pay?pa=${merchantVPA}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=INR&tn=${encodeURIComponent(transactionNote)}#Intent;scheme=upi;package=in.org.npci.upiapp;end`
        ];

        let methodIndex = 0;
        let appLaunched = false;

        function tryNextMethod() {
            if (methodIndex >= upiIntents.length || appLaunched) {
                if (!appLaunched && callback) {
                    callback(false, 'No UPI apps found or accessible');
                }
                return;
            }

            const currentIntent = upiIntents[methodIndex];
            console.log(`Trying UPI method ${methodIndex + 1}:`, currentIntent);

            try {
                if (this.isWebView()) {
                    // Method for WebView containers
                    const link = document.createElement('a');
                    link.href = currentIntent;
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                } else {
                    // Standard method
                    window.location.href = currentIntent;
                }

                // Check if app opened after 2 seconds
                setTimeout(() => {
                    if (!appLaunched) {
                        methodIndex++;
                        tryNextMethod.call(this);
                    }
                }, 2000);

                // Set a flag to detect if we're still in the browser
                setTimeout(() => {
                    if (document.hasFocus && document.hasFocus()) {
                        // Still in browser, try next method
                        if (!appLaunched) {
                            methodIndex++;
                            tryNextMethod.call(this);
                        }
                    } else {
                        // App likely opened
                        appLaunched = true;
                        if (callback) {
                            callback(true, 'UPI app launched successfully');
                        }
                    }
                }, 1500);

            } catch (error) {
                console.error(`UPI method ${methodIndex + 1} failed:`, error);
                methodIndex++;
                tryNextMethod.call(this);
            }
        }

        // Start trying methods
        tryNextMethod.call(this);
    },

    /**
     * Show mobile-friendly loading message
     */
    showMobileLoading: function(message = 'Opening Payment Gateway...') {
        const loadingHtml = `
            <div id="mobile-payment-loading" style="
                position: fixed; 
                top: 0; 
                left: 0; 
                width: 100%; 
                height: 100%; 
                background: rgba(0,0,0,0.8); 
                color: white; 
                display: flex; 
                align-items: center; 
                justify-content: center; 
                z-index: 9999;
                flex-direction: column;
                text-align: center;
                padding: 20px;
            ">
                <div style="font-size: 18px; margin-bottom: 15px;">
                    <i class="fas fa-mobile-alt" style="font-size: 24px; margin-bottom: 10px;"></i><br>
                    ${message}
                </div>
                <div style="font-size: 14px; color: #ccc; margin-bottom: 20px;">
                    When payment options appear, select your preferred UPI app<br>
                    (PhonePe, Google Pay, Paytm, etc.)
                </div>
                <div class="spinner" style="
                    border: 3px solid #f3f3f3;
                    border-top: 3px solid #ff7529;
                    border-radius: 50%;
                    width: 30px;
                    height: 30px;
                    animation: spin 1s linear infinite;
                "></div>
            </div>
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;
        
        document.body.insertAdjacentHTML('beforeend', loadingHtml);
    },

    /**
     * Hide mobile loading message
     */
    hideMobileLoading: function() {
        const loading = document.getElementById('mobile-payment-loading');
        if (loading) {
            loading.remove();
        }
    },

    /**
     * Enhanced Razorpay options for mobile
     */
    getRazorpayMobileConfig: function() {
        return {
            "config": {
                "display": {
                    "blocks": {
                        "utib": {
                            "name": "Pay using UPI Apps",
                            "instruments": [
                                {
                                    "method": "upi"
                                }
                            ]
                        }
                    },
                    "sequence": ["block.utib"],
                    "preferences": {
                        "show_default_blocks": true
                    }
                }
            },
            "modal": {
                "escape": false,
                "backdrop_close": false,
                "ondismiss": function(){
                    console.log('Razorpay modal dismissed in mobile environment');
                }
            }
        };
    },

    /**
     * Initialize mobile payment environment
     */
    init: function() {
        if (this.isMobileApp()) {
            console.log('Mobile payment environment detected');
            
            // Add mobile-specific styles
            const style = document.createElement('style');
            style.textContent = `
                .razorpay-payment-button {
                    font-size: 16px !important;
                    padding: 12px 24px !important;
                }
                
                .mobile-payment-info {
                    background: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                    margin: 10px 0;
                    text-align: center;
                    font-size: 14px;
                    color: #6c757d;
                }
            `;
            document.head.appendChild(style);
        }
    }
};

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        window.MobilePaymentHelper.init();
    });
} else {
    window.MobilePaymentHelper.init();
}
