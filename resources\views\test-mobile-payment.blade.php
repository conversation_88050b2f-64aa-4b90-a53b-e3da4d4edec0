<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Payment Test</title>
    <script src="{{ static_asset('assets/js/mobile-payment-helper.js') }}"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 0 auto;
        }
        .test-button {
            background: #ff7529;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 5px;
            width: 100%;
        }
        .test-button:hover {
            background: #e6661f;
        }
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .result-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            min-height: 50px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>Mobile Payment Test Page</h2>
        
        <div class="info-box">
            <strong>Environment Detection:</strong>
            <div id="environment-info">Loading...</div>
        </div>
        
        <div class="info-box">
            <strong>User Agent:</strong>
            <div id="user-agent" style="font-size: 12px; word-break: break-all;"></div>
        </div>
        
        <h3>Test UPI App Launch</h3>
        <button class="test-button" onclick="testUPILaunch()">
            Test UPI App Launch
        </button>
        
        <h3>Test Razorpay Mobile Config</h3>
        <button class="test-button" onclick="testRazorpayConfig()">
            Show Razorpay Mobile Config
        </button>
        
        <h3>Test Mobile Loading</h3>
        <button class="test-button" onclick="testMobileLoading()">
            Show Mobile Loading
        </button>
        
        <div class="result-box">
            <strong>Test Results:</strong>
            <div id="test-results">Click any test button to see results...</div>
        </div>
    </div>

    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateEnvironmentInfo();
            document.getElementById('user-agent').textContent = navigator.userAgent;
        });

        function updateEnvironmentInfo() {
            const info = document.getElementById('environment-info');
            if (window.MobilePaymentHelper) {
                const isMobile = window.MobilePaymentHelper.isMobileApp();
                const isWebView = window.MobilePaymentHelper.isWebView();
                
                info.innerHTML = `
                    <div>Mobile App: <strong>${isMobile ? 'YES' : 'NO'}</strong></div>
                    <div>WebView: <strong>${isWebView ? 'YES' : 'NO'}</strong></div>
                    <div>Helper Loaded: <strong>YES</strong></div>
                `;
            } else {
                info.innerHTML = '<div style="color: red;">MobilePaymentHelper not loaded!</div>';
            }
        }

        function testUPILaunch() {
            const results = document.getElementById('test-results');
            results.innerHTML = 'Testing UPI app launch...';
            
            if (window.MobilePaymentHelper) {
                window.MobilePaymentHelper.launchUPIApp(
                    'test@paytm',
                    'Test Merchant',
                    100,
                    'Test Payment',
                    function(success, message) {
                        results.innerHTML = `
                            <div>UPI Launch Result: <strong>${success ? 'SUCCESS' : 'FAILED'}</strong></div>
                            <div>Message: ${message}</div>
                            <div>Time: ${new Date().toLocaleTimeString()}</div>
                        `;
                    }
                );
            } else {
                results.innerHTML = '<div style="color: red;">MobilePaymentHelper not available!</div>';
            }
        }

        function testRazorpayConfig() {
            const results = document.getElementById('test-results');
            
            if (window.MobilePaymentHelper) {
                const config = window.MobilePaymentHelper.getRazorpayMobileConfig();
                results.innerHTML = `
                    <div><strong>Razorpay Mobile Config:</strong></div>
                    <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px; overflow-x: auto;">${JSON.stringify(config, null, 2)}</pre>
                `;
            } else {
                results.innerHTML = '<div style="color: red;">MobilePaymentHelper not available!</div>';
            }
        }

        function testMobileLoading() {
            const results = document.getElementById('test-results');
            
            if (window.MobilePaymentHelper) {
                results.innerHTML = 'Showing mobile loading... (will hide in 3 seconds)';
                
                window.MobilePaymentHelper.showMobileLoading('Testing Mobile Loading...');
                
                setTimeout(() => {
                    window.MobilePaymentHelper.hideMobileLoading();
                    results.innerHTML = `
                        <div>Mobile loading test completed</div>
                        <div>Time: ${new Date().toLocaleTimeString()}</div>
                    `;
                }, 3000);
            } else {
                results.innerHTML = '<div style="color: red;">MobilePaymentHelper not available!</div>';
            }
        }
    </script>
</body>
</html>
