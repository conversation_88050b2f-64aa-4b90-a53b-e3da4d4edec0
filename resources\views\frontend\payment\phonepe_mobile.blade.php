<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ translate('PhonePe Payment') }}</title>
    <script src="{{ static_asset('assets/js/mobile-payment-helper.js') }}"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #5f2c82, #49a09d);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .payment-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 400px;
            width: 100%;
        }
        .phonepe-logo {
            width: 120px;
            height: 40px;
            margin: 0 auto 20px;
            background: #5f2c82;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }
        .payment-amount {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin: 20px 0;
        }
        .payment-message {
            color: #666;
            margin: 15px 0;
            line-height: 1.5;
        }
        .btn {
            background: #5f2c82;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            min-width: 200px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #4a1f6b;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .loading {
            display: none;
            margin: 20px 0;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #5f2c82;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error-message {
            color: #dc3545;
            margin: 15px 0;
            padding: 10px;
            background: #f8d7da;
            border-radius: 5px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="phonepe-logo">PhonePe</div>
        
        @if(isset($amount))
        <div class="payment-amount">
            ₹{{ number_format($amount, 2) }}
        </div>
        @endif

        <div class="payment-message">
            {{ translate('Complete your payment using PhonePe app') }}
        </div>

        <div class="error-message" id="error-message">
            {{ translate('PhonePe app not found. Please install PhonePe app or use web payment.') }}
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>{{ translate('Opening PhonePe app...') }}</p>
        </div>

        <div id="payment-buttons">
            <button class="btn" onclick="openPhonePeApp()">
                {{ translate('Pay with UPI Apps') }}
            </button>

            <button class="btn btn-secondary" onclick="openWebPayment()">
                {{ translate('Pay via Web') }}
            </button>

            <button class="btn btn-outline-secondary btn-sm" onclick="testUPIDebug()" style="width: 100%; font-size: 12px; margin-top: 10px;">
                🔧 Debug UPI Launch
            </button>

            <div style="margin-top: 15px; font-size: 12px; color: #666;">
                {{ translate('Supports PhonePe, Google Pay, Paytm, and other UPI apps') }}
            </div>
        </div>
    </div>

    <script>
        const intentUrl = @json($intentUrl ?? '');
        const redirectUrl = @json($redirectUrl);
        const orderId = @json($orderId);

        let appOpenAttempted = false;
        let fallbackTimer = null;

        // Debug logging
        console.log('PhonePe Mobile Payment Debug:', {
            intentUrl: intentUrl,
            redirectUrl: redirectUrl,
            orderId: orderId,
            userAgent: navigator.userAgent,
            isMobileHelper: !!window.MobilePaymentHelper,
            helperDetection: window.MobilePaymentHelper ? window.MobilePaymentHelper.isMobileApp() : 'Helper not loaded'
        });

        function resetAppAttempt() {
            appOpenAttempted = false;
            if (fallbackTimer) {
                clearTimeout(fallbackTimer);
            }
        }

        function openPhonePeApp() {
            if (!redirectUrl) {
                showError('{{ translate("Payment URL not available") }}');
                return;
            }

            // Reset any previous attempt
            resetAppAttempt();

            showLoading();
            appOpenAttempted = true;

            // Create proper UPI intent URLs
            const amount = {{ $amount ?? 0 }};
            const merchantName = 'CloudMart';
            const transactionNote = 'Payment for Order';
            const merchantVPA = 'cloudmart@paytm';

            // Multiple UPI intent formats for better compatibility
            const upiIntents = [
                // Standard UPI intent
                `upi://pay?pa=${merchantVPA}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=INR&tn=${encodeURIComponent(transactionNote)}`,
                // Android intent format for WebView
                `intent://pay?pa=${merchantVPA}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=INR&tn=${encodeURIComponent(transactionNote)}#Intent;scheme=upi;package=com.phonepe.app;end`,
                // Google Pay intent
                `intent://pay?pa=${merchantVPA}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=INR&tn=${encodeURIComponent(transactionNote)}#Intent;scheme=upi;package=com.google.android.apps.nbu.paisa.user;end`,
                // Paytm intent
                `intent://pay?pa=${merchantVPA}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=INR&tn=${encodeURIComponent(transactionNote)}#Intent;scheme=upi;package=net.one97.paytm;end`
            ];

            console.log('Trying UPI intents for APK/WebView environment');

            // Try multiple methods for better compatibility
            let methodIndex = 0;

            function tryNextMethod() {
                if (methodIndex >= upiIntents.length) {
                    // All methods failed, try web payment
                    hideLoading();
                    showWebPaymentOption();
                    return;
                }

                const currentIntent = upiIntents[methodIndex];
                console.log(`Trying method ${methodIndex + 1}:`, currentIntent);

                try {
                    // For APK/WebView environments, try different approaches
                    if (isWebView()) {
                        // Method for WebView containers
                        const link = document.createElement('a');
                        link.href = currentIntent;
                        link.click();
                    } else {
                        // Standard method
                        window.location.href = currentIntent;
                    }

                    // Check if app opened after 2 seconds
                    setTimeout(() => {
                        if (appOpenAttempted) {
                            methodIndex++;
                            tryNextMethod();
                        }
                    }, 2000);

                } catch (error) {
                    console.error(`Method ${methodIndex + 1} failed:`, error);
                    methodIndex++;
                    tryNextMethod();
                }
            }

            // Use mobile payment helper if available, otherwise use direct method
            if (window.MobilePaymentHelper && window.MobilePaymentHelper.isMobileApp()) {
                console.log('Using MobilePaymentHelper for UPI launch');
                window.MobilePaymentHelper.launchUPIApp(
                    merchantVPA,
                    merchantName,
                    amount,
                    transactionNote,
                    function(success, message) {
                        if (success) {
                            console.log('UPI app launched successfully via helper');
                            hideLoading();
                            showUPISuccessMessage();
                        } else {
                            console.log('UPI app launch failed via helper, trying direct method:', message);
                            // Try direct method as fallback
                            tryNextMethod();
                        }
                    }
                );
            } else {
                console.log('MobilePaymentHelper not available, using direct method');
                tryNextMethod();
            }

        }

        // Check if running in WebView (APK environment)
        function isWebView() {
            const userAgent = navigator.userAgent;
            return userAgent.includes('wv') ||
                   userAgent.includes('WebView') ||
                   userAgent.includes('Version/') && userAgent.includes('Mobile Safari') ||
                   window.navigator.standalone === false;
        }

        // Show web payment option when UPI apps fail
        function showWebPaymentOption() {
            document.getElementById('payment-buttons').innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <div style="color: #ffc107; font-size: 16px; margin-bottom: 10px;">
                        <i class="fas fa-exclamation-triangle"></i> UPI Apps Not Available
                    </div>
                    <div style="color: #666; font-size: 14px; margin-bottom: 15px;">
                        UPI payment apps are not accessible in this environment.<br>
                        Please use web payment to complete your transaction.
                    </div>
                    <button class="btn" onclick="openWebPayment()" style="background: #ff7529; color: white; padding: 12px 24px; border: none; border-radius: 6px; font-size: 16px;">
                        <i class="fas fa-globe"></i> Continue with Web Payment
                    </button>
                    <div style="margin-top: 10px; font-size: 12px; color: #999;">
                        Secure payment via browser
                    </div>
                </div>
            `;
        }

            // Listen for page visibility change (app opened successfully) - but only once
            let visibilityHandled = false;
            function handleVisibilityChange() {
                if (document.hidden && appOpenAttempted && !visibilityHandled) {
                    visibilityHandled = true;
                    // App likely opened successfully
                    clearTimeout(fallbackTimer);
                    hideLoading();

                    // Show success message
                    document.getElementById('payment-buttons').innerHTML = `
                        <div style="text-align: center; padding: 20px;">
                            <div style="color: #28a745; font-size: 18px; margin-bottom: 10px;">
                                ✓ UPI App Opened Successfully
                            </div>
                            <div style="color: #666; font-size: 14px;">
                                Complete your payment in the UPI app and return here.
                            </div>
                            <button class="btn" onclick="checkPaymentStatus()" style="margin-top: 15px;">
                                Check Payment Status
                            </button>
                        </div>
                    `;

                    // Remove the event listener to prevent multiple triggers
                    document.removeEventListener('visibilitychange', handleVisibilityChange);
                }
            }

            document.addEventListener('visibilitychange', handleVisibilityChange);
        }

        function showUPISuccessMessage() {
            document.getElementById('payment-buttons').innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <div style="color: #28a745; font-size: 18px; margin-bottom: 10px;">
                        ✓ UPI App Opened Successfully
                    </div>
                    <div style="color: #666; font-size: 14px; margin-bottom: 20px;">
                        Complete your payment in the UPI app and return here.
                    </div>
                    <button class="btn btn-success" onclick="checkPaymentStatus()" style="margin-top: 15px; margin-right: 10px;">
                        Check Payment Status
                    </button>
                    <button class="btn btn-secondary" onclick="showWebPaymentOption()" style="margin-top: 15px;">
                        Use Web Payment Instead
                    </button>
                </div>
            `;
        }

        function showWebPaymentOption() {
            document.getElementById('payment-buttons').innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <div style="color: #ff6b35; font-size: 18px; margin-bottom: 15px;">
                        <i class="fas fa-exclamation-triangle"></i> UPI Apps Not Available
                    </div>
                    <div style="color: #666; font-size: 14px; margin-bottom: 20px;">
                        Continue with web payment instead
                    </div>
                    <button class="btn btn-primary" onclick="openWebPayment()" style="background: #ff7529; border: none; padding: 12px 30px;">
                        Continue with Web Payment
                    </button>
                </div>
            `;
        }

        function checkPaymentStatus() {
            showLoading();
            // Redirect to callback URL to check payment status
            window.location.href = redirectUrl;
        }

        function testUPIDebug() {
            console.log('=== UPI Debug Test ===');
            console.log('Environment Detection:', {
                userAgent: navigator.userAgent,
                isMobileHelper: !!window.MobilePaymentHelper,
                helperDetection: window.MobilePaymentHelper ? window.MobilePaymentHelper.isMobileApp() : 'Helper not loaded',
                isWebView: window.MobilePaymentHelper ? window.MobilePaymentHelper.isWebView() : 'Helper not loaded'
            });

            // Test simple UPI intent
            const testUPI = 'upi://pay?pa=test@paytm&pn=Test&am=1&cu=INR&tn=Test';
            console.log('Testing UPI intent:', testUPI);

            try {
                window.location.href = testUPI;
                setTimeout(() => {
                    console.log('UPI intent attempted - if no app opened, UPI apps may not be available');
                    alert('UPI intent attempted. Check console for details.');
                }, 1000);
            } catch (error) {
                console.error('UPI intent failed:', error);
                alert('UPI intent failed: ' + error.message);
            }
        }

        function openWebPayment() {
            showLoading();
            window.location.href = redirectUrl;
        }

        // Auto-trigger UPI app launch for better user experience
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a moment for the page to fully load
            setTimeout(function() {
                if (window.MobilePaymentHelper && window.MobilePaymentHelper.isMobileApp()) {
                    console.log('Auto-triggering UPI app launch for mobile environment');
                    // Auto-click the UPI button for better UX
                    // openPhonePeApp();
                }
            }, 1000);
        });

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('payment-buttons').style.display = 'none';
            document.getElementById('error-message').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('payment-buttons').style.display = 'block';
        }

        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            hideLoading();
        }

        function checkPaymentStatus() {
            showLoading();

            // Check payment status via AJAX
            fetch('{{ route("phonepe.callback") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    orderId: orderId,
                    check_status: true
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success && data.status === 'completed') {
                    // Payment successful
                    document.getElementById('payment-buttons').innerHTML = `
                        <div style="text-align: center; padding: 20px;">
                            <div style="color: #28a745; font-size: 20px; margin-bottom: 10px;">
                                ✓ Payment Successful!
                            </div>
                            <div style="color: #666; font-size: 14px; margin-bottom: 15px;">
                                Redirecting to order confirmation...
                            </div>
                        </div>
                    `;
                    setTimeout(() => {
                        window.location.href = '{{ route("order_confirmed") }}';
                    }, 2000);
                } else if (data.status === 'pending') {
                    // Payment still pending
                    document.getElementById('payment-buttons').innerHTML = `
                        <div style="text-align: center; padding: 20px;">
                            <div style="color: #ffc107; font-size: 18px; margin-bottom: 10px;">
                                ⏳ Payment Pending
                            </div>
                            <div style="color: #666; font-size: 14px; margin-bottom: 15px;">
                                Please complete the payment in your UPI app.
                            </div>
                            <button class="btn" onclick="checkPaymentStatus()">
                                Check Again
                            </button>
                            <button class="btn btn-secondary" onclick="openWebPayment()">
                                Use Web Payment
                            </button>
                        </div>
                    `;
                } else {
                    // Payment failed or unknown status
                    showError('Payment status unclear. Please check your UPI app or use web payment.');
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Error checking payment status:', error);
                showError('Unable to check payment status. Please try again.');
            });
        }

        // Manual trigger only - no auto-trigger to prevent continuous popups
        window.addEventListener('load', function() {
            // Just show instructions
            console.log('PhonePe mobile payment page loaded');
        });
    </script>
</body>
</html>
