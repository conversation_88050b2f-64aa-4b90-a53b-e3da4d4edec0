<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ translate('PhonePe Payment') }}</title>

    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #5f2c82, #49a09d);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .payment-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 400px;
            width: 100%;
        }
        .phonepe-logo {
            width: 120px;
            height: 40px;
            margin: 0 auto 20px;
            background: #5f2c82;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }
        .payment-amount {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin: 20px 0;
        }
        .payment-message {
            color: #666;
            margin: 15px 0;
            line-height: 1.5;
        }
        .btn {
            background: #5f2c82;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            min-width: 200px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #4a1f6b;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .loading {
            display: none;
            margin: 20px 0;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #5f2c82;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error-message {
            color: #dc3545;
            margin: 15px 0;
            padding: 10px;
            background: #f8d7da;
            border-radius: 5px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="phonepe-logo">PhonePe</div>
        
        @if(isset($amount))
        <div class="payment-amount">
            ₹{{ number_format($amount, 2) }}
        </div>
        @endif

        <div class="payment-message">
            {{ translate('Complete your payment using PhonePe app') }}
        </div>

        <div class="error-message" id="error-message">
            {{ translate('PhonePe app not found. Please install PhonePe app or use web payment.') }}
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>{{ translate('Opening PhonePe app...') }}</p>
        </div>

        <div id="payment-buttons">
            <button class="btn" onclick="openPhonePeApp()">
                {{ translate('Pay with UPI Apps') }}
            </button>

            <button class="btn btn-secondary" onclick="openWebPayment()">
                {{ translate('Pay via Web') }}
            </button>

            <button class="btn btn-outline-secondary btn-sm" onclick="testUPIDebug()" style="width: 100%; font-size: 12px; margin-top: 10px;">
                🔧 Debug UPI Launch
            </button>

            <button class="btn btn-outline-info btn-sm" onclick="testButtonClick()" style="width: 100%; font-size: 12px; margin-top: 5px;">
                🧪 Test Button Click
            </button>

            <div style="margin-top: 15px; font-size: 12px; color: #666;">
                {{ translate('Supports PhonePe, Google Pay, Paytm, and other UPI apps') }}
            </div>
        </div>
    </div>

    <script>
        const intentUrl = @json($intentUrl ?? '');
        const phonepeIntentUrl = @json($phonepeIntentUrl ?? '');
        const redirectUrl = @json($redirectUrl);
        const orderId = @json($orderId);
        const merchantVPA = @json($merchantVPA ?? 'cloudmart@paytm');
        const merchantName = @json($merchantName ?? 'CloudMart');

        let appOpenAttempted = false;
        let fallbackTimer = null;

        // Simple mobile detection
        function isMobileEnvironment() {
            const userAgent = navigator.userAgent;
            return /Mobile|Android|iPhone|iPad/.test(userAgent) ||
                   userAgent.includes('com.cloudmart.cloudmart') ||
                   userAgent.includes('wv') ||
                   userAgent.includes('WebView');
        }

        // Debug logging
        console.log('PhonePe Mobile Payment Debug:', {
            intentUrl: intentUrl,
            redirectUrl: redirectUrl,
            orderId: orderId,
            userAgent: navigator.userAgent,
            isMobile: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent),
            isAPK: navigator.userAgent.includes('com.cloudmart.cloudmart')
        });

        // Add UPI URL to page for debugging (without removing existing buttons)
        if (intentUrl || phonepeIntentUrl) {
            const debugDiv = document.createElement('div');
            debugDiv.style.cssText = 'margin-top: 20px; padding: 10px; background: #f8f9fa; border-radius: 5px; font-size: 12px;';
            debugDiv.innerHTML = `
                <strong>Debug Info:</strong><br>
                <div style="word-break: break-all; margin-top: 5px;">
                    Standard UPI URL: ${intentUrl}<br>
                    PhonePe URL: ${phonepeIntentUrl}<br>
                    Merchant: ${merchantVPA} (${merchantName})
                </div>
            `;
            document.getElementById('payment-buttons').appendChild(debugDiv);
        }

        function resetAppAttempt() {
            appOpenAttempted = false;
            if (fallbackTimer) {
                clearTimeout(fallbackTimer);
            }
        }

        function openPhonePeApp() {
            console.log('🚀 openPhonePeApp() button clicked!');
            if (!redirectUrl) {
                showError('{{ translate("Payment URL not available") }}');
                return;
            }

            // Reset any previous attempt
            resetAppAttempt();

            showLoading();
            appOpenAttempted = true;

            // Use UPI parameters from controller
            const amount = {{ $amount ?? 0 }};
            const transactionNote = 'Payment for Order ' + orderId;

            // Multiple UPI intent formats for better compatibility
            const upiIntents = [
                // Standard UPI intent
                `upi://pay?pa=${merchantVPA}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=INR&tn=${encodeURIComponent(transactionNote)}`,
                // Android intent format for WebView
                `intent://pay?pa=${merchantVPA}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=INR&tn=${encodeURIComponent(transactionNote)}#Intent;scheme=upi;package=com.phonepe.app;end`,
                // Google Pay intent
                `intent://pay?pa=${merchantVPA}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=INR&tn=${encodeURIComponent(transactionNote)}#Intent;scheme=upi;package=com.google.android.apps.nbu.paisa.user;end`,
                // Paytm intent
                `intent://pay?pa=${merchantVPA}&pn=${encodeURIComponent(merchantName)}&am=${amount}&cu=INR&tn=${encodeURIComponent(transactionNote)}#Intent;scheme=upi;package=net.one97.paytm;end`
            ];

            console.log('Trying UPI intents for APK/WebView environment');

            // Try each UPI intent directly
            let intentIndex = 0;
            let appLaunched = false;

            function tryNextIntent() {
                if (intentIndex >= upiIntents.length || appLaunched) {
                    if (!appLaunched) {
                        console.log('All UPI intents failed, showing web payment option');
                        hideLoading();
                        showWebPaymentOption();
                    }
                    return;
                }

                const currentIntent = upiIntents[intentIndex];
                console.log(`Trying UPI intent ${intentIndex + 1}/${upiIntents.length}:`, currentIntent);

                try {
                    // Method 1: Direct window.location
                    window.location.href = currentIntent;

                    // Method 2: Create and click link (backup)
                    setTimeout(() => {
                        if (!appLaunched) {
                            const link = document.createElement('a');
                            link.href = currentIntent;
                            link.style.display = 'none';
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                        }
                    }, 500);

                } catch (error) {
                    console.error(`UPI intent ${intentIndex + 1} failed:`, error);
                }

                intentIndex++;

                // Try next intent after delay
                setTimeout(() => {
                    if (!appLaunched) {
                        tryNextIntent();
                    }
                }, 2000);
            }

            // Start trying intents
            tryNextIntent();

            // Show success message after reasonable delay
            setTimeout(() => {
                if (!appLaunched) {
                    hideLoading();
                    showUPISuccessMessage();
                    appLaunched = true;
                }
            }, 4000);





        }

        // Check if running in WebView (APK environment)
        function isWebView() {
            const userAgent = navigator.userAgent;
            return userAgent.includes('wv') ||
                   userAgent.includes('WebView') ||
                   userAgent.includes('Version/') && userAgent.includes('Mobile Safari') ||
                   window.navigator.standalone === false;
        }

        // Show web payment option when UPI apps fail
        function showWebPaymentOption() {
            console.log('Showing web payment option');
            // Just trigger web payment directly instead of replacing buttons
            openWebPayment();
        }

            // Listen for page visibility change (app opened successfully) - but only once
            let visibilityHandled = false;
            function handleVisibilityChange() {
                if (document.hidden && appOpenAttempted && !visibilityHandled) {
                    visibilityHandled = true;
                    // App likely opened successfully
                    clearTimeout(fallbackTimer);
                    hideLoading();
                    showUPISuccessMessage();
                    // Remove the event listener to prevent multiple triggers
                    document.removeEventListener('visibilitychange', handleVisibilityChange);
                }
            }

            document.addEventListener('visibilitychange', handleVisibilityChange);
        }

        function showUPISuccessMessage() {
            console.log('UPI app launched successfully');
            // Add a success message without replacing buttons
            const successDiv = document.createElement('div');
            successDiv.id = 'upi-success-message';
            successDiv.style.cssText = 'text-align: center; padding: 15px; margin-top: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; color: #155724;';
            successDiv.innerHTML = `
                <div style="font-size: 16px; margin-bottom: 10px;">
                    ✓ UPI App Opened Successfully
                </div>
                <div style="font-size: 14px; margin-bottom: 15px;">
                    Complete your payment in the UPI app and return here.
                </div>
                <button class="btn btn-success btn-sm" onclick="checkPaymentStatus()" style="margin-right: 10px;">
                    Check Payment Status
                </button>
                <button class="btn btn-secondary btn-sm" onclick="openWebPayment()">
                    Use Web Payment Instead
                </button>
            `;

            // Remove any existing success message
            const existing = document.getElementById('upi-success-message');
            if (existing) existing.remove();

            // Add the success message
            document.getElementById('payment-buttons').appendChild(successDiv);
        }



        function checkPaymentStatus() {
            showLoading();
            // Redirect to callback URL to check payment status
            window.location.href = redirectUrl;
        }

        function testButtonClick() {
            console.log('🧪 testButtonClick() button clicked!');
            alert('✅ Button Click Test Successful!\n\nThis confirms JavaScript is working properly.');
        }

        function testUPIDebug() {
            console.log('🔧 testUPIDebug() button clicked!');
            console.log('=== UPI Debug Test ===');
            console.log('Environment Detection:', {
                userAgent: navigator.userAgent,
                isMobile: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent),
                isAPK: navigator.userAgent.includes('com.cloudmart.cloudmart'),
                isWebView: navigator.userAgent.includes('wv') || navigator.userAgent.includes('WebView')
            });

            // Test multiple UPI intent formats
            const testIntents = [
                'upi://pay?pa=test@paytm&pn=Test&am=1&cu=INR&tn=Test',
                'intent://pay?pa=test@paytm&pn=Test&am=1&cu=INR&tn=Test#Intent;scheme=upi;package=com.phonepe.app;end',
                'intent://pay?pa=test@paytm&pn=Test&am=1&cu=INR&tn=Test#Intent;scheme=upi;package=com.google.android.apps.nbu.paisa.user;end',
                'intent://pay?pa=test@paytm&pn=Test&am=1&cu=INR&tn=Test#Intent;scheme=upi;package=net.one97.paytm;end'
            ];

            let currentIndex = 0;

            function tryNextIntent() {
                if (currentIndex >= testIntents.length) {
                    console.log('All UPI intents tested');
                    alert('All UPI intents tested. Check console for details.');
                    return;
                }

                const intent = testIntents[currentIndex];
                console.log(`Testing intent ${currentIndex + 1}:`, intent);

                try {
                    // Create a temporary link and click it
                    const link = document.createElement('a');
                    link.href = intent;
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    currentIndex++;
                    setTimeout(tryNextIntent, 2000); // Try next after 2 seconds
                } catch (error) {
                    console.error(`Intent ${currentIndex + 1} failed:`, error);
                    currentIndex++;
                    setTimeout(tryNextIntent, 1000);
                }
            }

            tryNextIntent();
        }

        function openWebPayment() {
            console.log('🌐 openWebPayment() button clicked!');
            showLoading();
            window.location.href = redirectUrl;
        }

        // Manual trigger only - no auto-trigger to prevent continuous popups
        document.addEventListener('DOMContentLoaded', function() {
            console.log('PhonePe mobile payment page loaded - ready for manual button clicks');
        });

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('payment-buttons').style.display = 'none';
            document.getElementById('error-message').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('payment-buttons').style.display = 'block';
        }

        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            hideLoading();
        }



        // Manual trigger only - no auto-trigger to prevent continuous popups
        window.addEventListener('load', function() {
            // Just show instructions
            console.log('PhonePe mobile payment page loaded');
        });
    </script>
</body>
</html>
