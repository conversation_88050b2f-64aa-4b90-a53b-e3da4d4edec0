@extends('frontend.suruchi.layouts.app')

@section('meta')
    @if (get_setting('otp_system'))
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/css/intlTelInput.css">
        <style>
            .phone-form-group .iti {
                width: 100%;
                display: block;
                position: relative;
            }
            .phone-form-group .iti__country-list {
                z-index: 9999;
                max-height: 200px;
                overflow-y: auto;
            }
            .phone-form-group .iti__selected-flag {
                padding: 0 8px;
                height: 100%;
                display: flex;
                align-items: center;
                border-right: 1px solid #ddd;
            }
            .phone-form-group .account__login--input {
                padding-left: 80px !important;
                width: 100%;
                border: none;
                outline: none;
            }
            .phone-form-group .iti__flag-container {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                padding: 1px;
                z-index: 2;
                background: #fff;
                border-radius: 4px 0 0 4px;
            }
            .phone-form-group {
                position: relative;
                width: 100%;
                margin-bottom: 15px;
            }
            .phone-form-group .iti__arrow {
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #999;
                margin-left: 4px;
            }
            .phone-form-group .iti__selected-dial-code {
                color: #666;
                margin-left: 6px;
                font-size: 14px;
            }
            .phone-form-group .iti input[type=tel] {
                padding-left: 80px;
                border: 1px solid #ddd;
                border-radius: 4px;
                height: 50px;
                font-size: 14px;
            }
        </style>
    @endif
@endsection

@section('content')
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <!-- Add Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        /* Custom styles to fix Select2 display issues */
        .select2-container {
            width: 100% !important;
        }
        .select2-container--default .select2-selection--single {
            height: auto;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #ced4da;
        }
        .select2-container .selection{
            width: 100% !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 1.5;
            padding-left: 0;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 100%;
        }
        /* Make sure the dropdown appears above other elements */
        .select2-dropdown {
            z-index: 9999;
        }
    </style>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <div class="login__section section--padding">
        <div class="container">
            <form id="reg-form" class="form-default" action="{{ route('shops.store') }}" method="POST">
                @csrf
                <div class="login__section--inner">
                    <div class="row">
                        <div class="col-md-3 col-lg-3 col-sm-0"></div>
                        <div class="col-md-6 col-lg-6">
                            <div class="account__login register">
                                <!-- Title Section -->
                                <div class="account__login--header mb-25">
                                    <h2 class="account__login--header__title h3 mb-10">{{ translate('Register as seller') }}</h2>
                                    <p class="account__login--header__desc">{{ translate('Register here if you are a new seller') }}</p>
                                </div>

                                <!-- Form Section -->
                                <div class="account__login--inner">
                                    <!-- Name Field -->
                                    <input class="account__login--input" name="name" placeholder="{{ translate('Full Name') }}" type="text" value="{{ old('name') }}" required>
                                    @error('name')
                                    <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror

                                    <!-- Email Field -->
                                    <input class="account__login--input" name="email" placeholder="{{ translate('Email') }}" type="email" value="{{ old('email') }}" required>
                                    @error('email')
                                    <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror

                                    <!-- Phone Field with Country Code -->
                                    @if (get_setting('otp_system'))
                                        <div class="phone-form-group">
                                            <input id="seller-phone-code" class="account__login--input" name="phone" placeholder="{{ translate('Phone Number') }}" type="tel" value="{{ old('phone') }}" autocomplete="off" required>
                                            @error('phone')
                                            <span class="invalid-feedback d-block">{{ $message }}</span>
                                            @enderror
                                            <!-- Country Code -->
                                            <input type="hidden" name="country_code" value="+91" required>
                                            @error('country_code')
                                            <span class="invalid-feedback d-block">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    @endif

                                    <!-- Password Fields -->
                                    <input class="account__login--input" name="password" placeholder="{{ translate('Password') }}" type="password" required>
                                    @error('password')
                                    <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                    <input class="account__login--input" name="password_confirmation" placeholder="{{ translate('Confirm Password') }}" type="password" required>

                                    <!-- Shop Info -->
                                    <input class="account__login--input" name="shop_name" placeholder="{{ translate('Registered Name') }}" type="text" value="{{ old('shop_name') }}" required>
                                    @error('shop_name')
                                    <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror

                                    <input class="account__login--input" name="address" placeholder="{{ translate('Address') }}" type="text" value="{{ old('address') }}" required>
                                    @error('address')
                                    <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror

                                    <!-- GST Number Field (Optional) -->
                                    <input class="account__login--input" name="gst_number" placeholder="{{ translate('GST Number (Optional)') }}" type="text" value="{{ old('gst_number') }}">
                                    @error('gst_number')
                                    <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror

                                    <!-- City Dropdown with Select2 -->
                                    <div class="select2-container-wrapper">
                                        <select class="account__login--input select2" id="city-select" name="city_id" required>
                                            <option value="">{{ translate('Select City') }}</option>
                                            @foreach ($cities as $city)
                                                <option value="{{ $city->id }}" 
                                                    {{ old('city_id') == $city->id ? 'selected' : '' }}
                                                    data-lat="{{ $city->latitude ?? '' }}" 
                                                    data-lng="{{ $city->longitude ?? '' }}">
                                                    {{ $city->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @error('city_id')
                                    <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror

                                    <!-- Map -->
                                    <label for="map" class="mt-3">{{ translate('Drag and drop the pin to locate your shop') }}</label>
                                    <div id="map" style="height: 300px;"></div>
                                    <input class="account__login--input" name="latitude" placeholder="{{ translate('Latitude') }}" type="text" id="latitude" value="{{ old('latitude') }}" required>
                                    @error('latitude')
                                    <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                    <input class="account__login--input" name="longitude" placeholder="{{ translate('Longitude') }}" type="text" id="longitude" value="{{ old('longitude') }}" required>
                                    @error('longitude')
                                    <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror

                                    <!-- Recaptcha -->
                                    @if(get_setting('google_recaptcha') == 1)
                                        <div class="g-recaptcha" data-sitekey="{{ env('CAPTCHA_KEY') }}"></div>
                                        @error('g-recaptcha-response')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                        @enderror
                                    @endif

                                    <!-- Terms and Conditions -->
                                    <label class="checkout__checkbox--label">
                                        <input type="checkbox" name="terms" required>
                                        {{ translate('By signing up you agree to our ') }}
                                        <a href="{{ route('terms') }}" class="fw-500">{{ translate('terms and conditions') }}</a>.
                                    </label>

                                    <!-- Submit Button -->
                                    <button class="account__login--btn primary__btn mb-10" type="submit">{{ translate('Register as seller') }}</button>

                                    <!-- Social Login -->
                                    @if(get_setting('google_login') || get_setting('facebook_login') || get_setting('twitter_login') || get_setting('apple_login'))
                                        <div class="account__login--divide"><span class="account__login--divide__text">OR</span></div>
                                        <div class="account__social d-flex justify-content-center mb-15">
                                            @if(get_setting('facebook_login'))
                                                <a href="{{ route('social.login', ['provider' => 'facebook']) }}" class="account__social--link facebook">Facebook</a>
                                            @endif
                                            @if(get_setting('google_login'))
                                                <a href="{{ route('social.login', ['provider' => 'google']) }}" class="account__social--link google">Google</a>
                                            @endif
                                            @if(get_setting('twitter_login'))
                                                <a href="{{ route('social.login', ['provider' => 'twitter']) }}" class="account__social--link twitter">Twitter</a>
                                            @endif
                                            @if(get_setting('apple_login'))
                                                <a href="{{ route('social.login', ['provider' => 'apple']) }}" class="account__social--link apple">Apple</a>
                                            @endif
                                        </div>
                                    @endif

                                    <!-- Log In Link -->
                                    <p>{{ translate('Already have an account?') }} <a href="{{ route('seller.login') }}">{{ translate('Log In') }}</a></p>
                                    <a href="{{ url()->previous() }}" class="go-back"><i class="las la-arrow-left"></i> {{ translate('Back to Previous Page') }}</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

@endsection

@section('script')
    <!-- Add jQuery if not already included in your project -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Add Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <!-- Initialize Select2 with a delay to ensure DOM is fully loaded -->
    <script>
        // Check if jQuery is loaded
        window.addEventListener('load', function() {
            if (typeof jQuery !== 'undefined') {
                console.log('jQuery is loaded');
                initializeSelect2();
            } else {
                console.error('jQuery is not loaded');
            }
        });
        
        function initializeSelect2() {
            setTimeout(function() {
                console.log('Initializing Select2');
                if ($.fn.select2) {
                    $('#city-select').select2({
                        placeholder: "Select City",
                        allowClear: true,
                        width: '100%',
                        dropdownParent: $('body')
                    });
                    console.log('Select2 initialized');
                } else {
                    console.error('Select2 plugin not found');
                }
            }, 300);
        }
    </script>
    
    @if(get_setting('google_recaptcha') == 1)
        <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    @endif

    @if (get_setting('otp_system'))
        <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/intlTelInput.min.js"></script>
    @endif

    <script type="text/javascript">
        @if(get_setting('google_recaptcha') == 1)
        // making the CAPTCHA a required field for form submission
        $(document).ready(function(){
            $("#reg-form").on("submit", function(evt)
            {
                var response = grecaptcha.getResponse();
                if(response.length == 0)
                {
                    //reCaptcha not verified
                    alert("please verify you are human!");
                    evt.preventDefault();
                    return false;
                }
                //captcha verified
                //do the rest of your validations here
                $("#reg-form").submit();
            });
        });
        @endif

        // Initialize Select2
        $(document).ready(function() {
            // Force destroy any existing Select2 instances first
            if ($.fn.select2 !== undefined) {
                $('.select2').select2('destroy');
            }
            
            // Re-initialize with more specific options
            $('.select2').select2({
                placeholder: "{{ translate('Select City') }}",
                allowClear: true,
                width: '100%',
                dropdownParent: $('body'),
                theme: 'classic',
                minimumResultsForSearch: 5
            });
            
            // Apply custom styling after initialization
            setTimeout(function() {
                $('.select2-container--default .select2-selection--single').css({
                    'height': 'auto',
                    'padding': '8px 12px',
                    'border-radius': '4px',
                    'border': '1px solid #ced4da'
                });
                
                // Ensure the container width matches the original select
                $('.select2-container').css('width', '100%');
            }, 100);
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Make sure input elements are available
            const latitudeInput = document.getElementById('latitude');
            const longitudeInput = document.getElementById('longitude');

            // Check if the latitude and longitude input fields exist
            if (!latitudeInput || !longitudeInput) {
                console.error("Latitude or Longitude input element not found.");
                return;
            }

            // Set initial location to Bangalore
            const initialLat = 12.9715987; // Bangalore's latitude
            const initialLng = 77.5945627; // Bangalore's longitude

            // Initialize map
            const map = L.map('map').setView([initialLat, initialLng], 13);

            // Add OpenStreetMap tiles
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 19,
                attribution: '&copy; <a href="https://openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);

            // Add a draggable marker
            const marker = L.marker([initialLat, initialLng], { draggable: true }).addTo(map);

            // Update latitude and longitude fields when the marker is dragged
            marker.on('dragend', function (e) {
                const latLng = e.target.getLatLng();
                latitudeInput.value = latLng.lat;
                longitudeInput.value = latLng.lng;
            });

            // Set initial values in latitude and longitude fields
            latitudeInput.value = initialLat;
            longitudeInput.value = initialLng;

            // Optionally: click on map to move marker
            map.on('click', function(e) {
                marker.setLatLng(e.latlng);
                latitudeInput.value = e.latlng.lat;
                longitudeInput.value = e.latlng.lng;
            });
            
            // Make map and marker variables accessible globally for the updateMapOnCityChange function
            window.map = map;
            window.marker = marker;
        });

        function updateMapOnCityChange() {
            const citySelect = document.getElementById('city-select');
            const latitudeInput = document.getElementById('latitude');
            const longitudeInput = document.getElementById('longitude');
            const map = window.map;
            const marker = window.marker;

            if (!citySelect || !latitudeInput || !longitudeInput || !map || !marker) {
                console.error("Required element(s) not found.");
                return;
            }

            // For jQuery-based Select2 events
            $('#city-select').on('select2:select', function (e) {
                const selectedOption = $(this).find(':selected');
                const newLat = parseFloat(selectedOption.attr('data-lat'));
                const newLng = parseFloat(selectedOption.attr('data-lng'));

                if (newLat && newLng) {
                    // Update map view and marker position
                    map.setView([newLat, newLng], 13);
                    marker.setLatLng([newLat, newLng]);

                    // Update latitude and longitude input fields
                    latitudeInput.value = newLat;
                    longitudeInput.value = newLng;
                }
            });
        }

        // Call the function after the DOM and Select2 have been initialized
        $(document).ready(function () {
            updateMapOnCityChange();
        });

        @if (get_setting('otp_system'))
        // Initialize intl-tel-input for seller registration phone
        $(document).ready(function() {
            var phoneInput = document.querySelector("#seller-phone-code");
            if (phoneInput) {
                var iti = window.intlTelInput(phoneInput, {
                    initialCountry: "auto",
                    geoIpLookup: function(callback) {
                        fetch('https://ipapi.co/json')
                            .then(function(res) { return res.json(); })
                            .then(function(data) { callback(data.country_code); })
                            .catch(function() { callback("in"); }); // Default to India
                    },
                    utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@18.2.1/build/js/utils.js",
                    separateDialCode: true,
                    nationalMode: false,
                    formatOnDisplay: true,
                    autoPlaceholder: "aggressive"
                });

                // Update country code when country changes
                phoneInput.addEventListener('countrychange', function() {
                    var countryCode = iti.getSelectedCountryData().dialCode;
                    $('input[name="country_code"]').val('+' + countryCode);
                });

                // Set initial country code when ready
                phoneInput.addEventListener('ready', function() {
                    var countryCode = iti.getSelectedCountryData().dialCode;
                    $('input[name="country_code"]').val('+' + countryCode);
                });

                // Update phone number format on form submission
                $('#reg-form').on('submit', function(e) {
                    var phoneNumber = iti.getNumber().replace('+' + iti.getSelectedCountryData().dialCode, '');
                    var countryCode = '+' + iti.getSelectedCountryData().dialCode;

                    if (phoneNumber) {
                        $('#seller-phone-code').val(phoneNumber);
                    }

                    // Ensure country code is set
                    $('input[name="country_code"]').val(countryCode);
                });
            }
        });
        @endif
    </script>
@endsection