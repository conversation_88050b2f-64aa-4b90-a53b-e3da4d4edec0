<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Shop extends Model
{

  protected $with = ['user'];

  protected $fillable = [
    'user_id', 'name', 'address', 'gst_number', 'city_id', 'latitude', 'longitude', 'slug'
  ];

  public function user()
  {
    return $this->belongsTo(User::class);
  }

  public function seller_package(){
    return $this->belongsTo(SellerPackage::class);
  }
  public function followers(){
    return $this->hasMany(FollowSeller::class);
  }
}
