<?php return array (
  'aiz-packages/combination-generate' => 
  array (
    'providers' => 
    array (
      0 => 'AizPackages\\CombinationGenerate\\Providers\\CombinationServiceProvider',
    ),
  ),
  'anandsiddharth/laravel-paytm-wallet' => 
  array (
    'providers' => 
    array (
      0 => 'Anand\\LaravelPaytmWallet\\PaytmWalletServiceProvider',
    ),
    'aliases' => 
    array (
      'PaytmWallet' => 'Anand\\LaravelPaytmWallet\\Facades\\PaytmWallet',
    ),
  ),
  'barryvdh/laravel-debugbar' => 
  array (
    'providers' => 
    array (
      0 => 'Barryvdh\\Debugbar\\ServiceProvider',
    ),
    'aliases' => 
    array (
      'Debugbar' => 'Barryvdh\\Debugbar\\Facades\\Debugbar',
    ),
  ),
  'barryvdh/laravel-ide-helper' => 
  array (
    'providers' => 
    array (
      0 => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
    ),
  ),
  'fideloper/proxy' => 
  array (
    'providers' => 
    array (
      0 => 'Fideloper\\Proxy\\TrustedProxyServiceProvider',
    ),
  ),
  'genealabs/laravel-sign-in-with-apple' => 
  array (
    'providers' => 
    array (
      0 => '\\GeneaLabs\\LaravelSignInWithApple\\Providers\\ServiceProvider',
    ),
  ),
  'genealabs/laravel-socialiter' => 
  array (
    'providers' => 
    array (
      0 => '\\GeneaLabs\\LaravelSocialiter\\Providers\\ServiceProvider',
    ),
    'aliases' => 
    array (
      'Socialiter' => '\\GeneaLabs\\LaravelSocialiter\\Facades\\Socialiter',
    ),
  ),
  'intervention/image' => 
  array (
    'providers' => 
    array (
      0 => 'Intervention\\Image\\ImageServiceProvider',
    ),
    'aliases' => 
    array (
      'Image' => 'Intervention\\Image\\Facades\\Image',
    ),
  ),
  'kingflamez/laravelrave' => 
  array (
    'providers' => 
    array (
      0 => 'KingFlamez\\Rave\\RaveServiceProvider',
    ),
    'aliases' => 
    array (
      'Rave' => 'KingFlamez\\Rave\\Facades\\Rave',
    ),
  ),
  'laracasts/flash' => 
  array (
    'providers' => 
    array (
      0 => 'Laracasts\\Flash\\FlashServiceProvider',
    ),
    'aliases' => 
    array (
      'Flash' => 'Laracasts\\Flash\\Flash',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/socialite' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    ),
    'aliases' => 
    array (
      'Socialite' => 'Laravel\\Socialite\\Facades\\Socialite',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'laravel/ui' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Ui\\UiServiceProvider',
    ),
  ),
  'maatwebsite/excel' => 
  array (
    'providers' => 
    array (
      0 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    ),
    'aliases' => 
    array (
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
    ),
  ),
  'mehedi-iitdu/core-component-repository' => 
  array (
    'providers' => 
    array (
      0 => 'MehediIitdu\\CoreComponentRepository\\CoreComponentRepositoryServiceProvider',
    ),
    'aliases' => 
    array (
      'CoreComponentRepository' => 'MehediIitdu\\CoreComponentRepository\\CoreComponentRepositoryFacade',
    ),
  ),
  'myfatoorah/laravel-package' => 
  array (
    'providers' => 
    array (
      0 => 'MyFatoorah\\LaravelPackage\\MyFatoorahServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'niklasravnsborg/laravel-pdf' => 
  array (
    'providers' => 
    array (
      0 => 'niklasravnsborg\\LaravelPdf\\PdfServiceProvider',
    ),
    'aliases' => 
    array (
      'PDF' => 'niklasravnsborg\\LaravelPdf\\Facades\\Pdf',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'sebacarrasco93/laravel-payku' => 
  array (
    'providers' => 
    array (
      0 => 'SebaCarrasco93\\LaravelPayku\\LaravelPaykuServiceProvider',
      1 => 'SebaCarrasco93\\LaravelPayku\\RouteServiceProvider',
    ),
    'aliases' => 
    array (
      'LaravelPayku' => 'SebaCarrasco93\\LaravelPayku\\Facades\\LaravelPayku',
    ),
  ),
  'simplesoftwareio/simple-qrcode' => 
  array (
    'providers' => 
    array (
      0 => 'SimpleSoftwareIO\\QrCode\\QrCodeServiceProvider',
    ),
    'aliases' => 
    array (
      'QrCode' => 'SimpleSoftwareIO\\QrCode\\Facades\\QrCode',
    ),
  ),
  'spatie/laravel-ignition' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    ),
    'aliases' => 
    array (
      'Flare' => 'Spatie\\LaravelIgnition\\Facades\\Flare',
    ),
  ),
  'spatie/laravel-permission' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Permission\\PermissionServiceProvider',
    ),
  ),
  'unicodeveloper/laravel-paystack' => 
  array (
    'providers' => 
    array (
      0 => 'Unicodeveloper\\Paystack\\PaystackServiceProvider',
    ),
    'aliases' => 
    array (
      'Paystack' => 'Unicodeveloper\\Paystack\\Facades\\Paystack',
    ),
  ),
);