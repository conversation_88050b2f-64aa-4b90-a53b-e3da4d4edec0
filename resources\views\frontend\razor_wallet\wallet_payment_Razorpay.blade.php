@php
    $theme = get_setting('homepage_select');
@endphp

@extends('frontend.'.$theme.'.layouts.app')

@section('content')

@endsection

@section('script')
    <script src="{{ static_asset('assets/js/mobile-payment-helper.js') }}"></script>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <script>
        // Check if running in mobile app/APK environment
        function isMobileApp() {
            const userAgent = navigator.userAgent;
            return userAgent.includes('wv') ||
                   userAgent.includes('WebView') ||
                   (userAgent.includes('Version/') && userAgent.includes('Mobile Safari')) ||
                   window.navigator.standalone === false;
        }

        var options = {
            "key": "{{ env('RAZOR_KEY') }}", // Enter the Key ID generated from the Dashboard
            "amount": "{{ Session::get('payment_data')['amount'] * 100 }}", // Amount is in currency subunits. Default currency is INR. Hence, 50000 refers to 50000 paise
            "currency": "INR",
            "name": "{{ env('APP_NAME') }}", //your business name
            "description": "{{ Session::get('payment_type') }}",
            "image": "{{ uploaded_asset(get_setting('header_logo')) }}",
            "order_id": "{{ $res->id }}", //This is a sample Order ID. Pass the `id` obtained in the response of Step 1
            "callback_url": "{{ route('payment.rozer') }}",
            "prefill": { //We recommend using the prefill parameter to auto-fill customer's contact information especially their phone number
                "name": "{{ Auth::user()->name }}", //your customer's name
                "email": "{{ Auth::user()->email ?? '' }}",
                @if(Auth::user()->phone)
                "contact": "{{ Auth::user()->phone }}" //Provide the customer's phone number for better conversion rates
                @endif
            },
            "notes": {
                "user_id": "{{ auth()->id() }}"
            },
            "theme": {
                "color": "#ff7529"
            },
            // Enhanced mobile configuration for APK environments
            "config": {
                "display": {
                    "blocks": {
                        "utib": { //Name of the bank
                            "name": "Pay using UPI Apps",
                            "instruments": [
                                {
                                    "method": "upi"
                                }
                            ]
                        }
                    },
                    "sequence": ["block.utib"],
                    "preferences": {
                        "show_default_blocks": true
                    }
                }
            }
        };
        var rzp1 = new Razorpay(options);

        $(document).ready(function() {
            // Enhanced mobile handling using MobilePaymentHelper
            if (window.MobilePaymentHelper && window.MobilePaymentHelper.isMobileApp()) {
                console.log('Mobile app environment detected for wallet payment');

                // Merge mobile configuration
                const mobileConfig = window.MobilePaymentHelper.getRazorpayMobileConfig();
                options = { ...options, ...mobileConfig };

                // Show mobile loading message
                window.MobilePaymentHelper.showMobileLoading('Opening Wallet Payment Gateway...');

                // Remove loading message after 3 seconds
                setTimeout(() => {
                    window.MobilePaymentHelper.hideMobileLoading();
                }, 3000);
            }
            rzp1.open();
        });
        $('#modal-close').click(function(){
            window.location = "{{ route('wallet.index') }}";
        });
    </script>

@endsection
